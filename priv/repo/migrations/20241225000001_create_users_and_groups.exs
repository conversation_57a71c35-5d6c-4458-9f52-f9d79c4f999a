defmodule Drops.TestRepo.Migrations.CreateUsersAndGroups do
  use Ecto.Migration

  def change do
    create table(:users) do
      add :name, :string, null: false
      add :email, :string

      timestamps()
    end

    create table(:groups) do
      add :name, :string, null: false
      add :description, :string

      timestamps()
    end

    create table(:user_groups, primary_key: false) do
      add :user_id, references(:users, on_delete: :delete_all), null: false
      add :group_id, references(:groups, on_delete: :delete_all), null: false
    end

    create unique_index(:user_groups, [:user_id, :group_id])
  end
end
