defmodule Test.Ecto.UserGroupSchemas do
  @moduledoc """
  Test schemas for User and Group associations used in cast/1 testing.
  """

  defmodule User do
    @moduledoc "User schema with has_many groups association"
    use Ecto.Schema
    import Ecto.Changeset

    schema "users" do
      field(:name, :string)
      field(:email, :string)
      
      has_many(:groups, Test.Ecto.UserGroupSchemas.Group)

      timestamps()
    end

    def changeset(user, attrs) do
      user
      |> cast(attrs, [:name, :email])
      |> validate_required([:name])
    end
  end

  defmodule Group do
    @moduledoc "Group schema with belongs_to user association"
    use Ecto.Schema
    import Ecto.Changeset

    schema "groups" do
      field(:name, :string)
      field(:description, :string)
      
      belongs_to(:user, Test.Ecto.UserGroupSchemas.User)

      timestamps()
    end

    def changeset(group, attrs) do
      group
      |> cast(attrs, [:name, :description, :user_id])
      |> validate_required([:name])
    end
  end
end
